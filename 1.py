import requests
import time
from datetime import datetime

def test_url_accessibility(code):
    """测试指定代码的URL是否可访问"""

    cookies = {
        'JSESSIONID': '5457B798BEACD8FA896071DB7528046C',
    }

    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'DNT': '1',
        'Origin': 'http://search.gjsy.gov.cn',
        'Pragma': 'no-cache',
        'Proxy-Connection': 'keep-alive',
        'Referer': f'http://search.gjsy.gov.cn/index.jsp?t=12&c={code}&at=0&sn=1&mc=20',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
    }

    data = {
        'c': str(code),
        't': '12',
        'at': '0',
        'sn': '1',
        'mc': '20',
        'ts': str(int(time.time() * 1000)),  # 使用当前时间戳
        'w': 'null',
        'h': 'null'
    }

    try:
        response = requests.post('http://search.gjsy.gov.cn/bjtz/query',
                               headers=headers,
                               cookies=cookies,
                               data=data,
                               verify=False,
                               timeout=10)  # 设置10秒超时

        status_code = response.status_code
        is_accessible = status_code == 200

        # 获取当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 判断响应内容是否有效（可以根据实际情况调整判断逻辑）
        content_valid = len(response.text) > 100  # 简单判断：内容长度大于100字符

        print(f"[{current_time}] 代码: {code} | 状态码: {status_code} | 可访问: {'是' if is_accessible else '否'} | 内容有效: {'是' if content_valid else '否'}")

        return {
            'code': code,
            'status_code': status_code,
            'is_accessible': is_accessible,
            'content_valid': content_valid,
            'response_length': len(response.text),
            'timestamp': current_time
        }

    except requests.exceptions.RequestException as e:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] 代码: {code} | 错误: {str(e)} | 可访问: 否")

        return {
            'code': code,
            'status_code': None,
            'is_accessible': False,
            'content_valid': False,
            'error': str(e),
            'timestamp': current_time
        }

def main():
    """主函数：测试从659006到659100的所有代码"""
    print("开始测试网页可访问性...")
    print("测试范围：659006 - 659100")
    print("测试间隔：每1秒一个")
    print("-" * 80)

    results = []

    # 从659006到659100进行测试
    for code in range(659100, 65999):  # 659101是为了包含659100
        result = test_url_accessibility(code)
        results.append(result)

        # 等待1秒再进行下一个测试
        time.sleep(1)

    print("-" * 80)
    print("测试完成！")

    # 统计结果
    accessible_count = sum(1 for r in results if r['is_accessible'])
    total_count = len(results)

    print(f"总测试数量: {total_count}")
    print(f"可访问数量: {accessible_count}")
    print(f"不可访问数量: {total_count - accessible_count}")
    print(f"可访问率: {accessible_count/total_count*100:.2f}%")

    # 显示可访问的代码列表
    accessible_codes = [r['code'] for r in results if r['is_accessible']]
    if accessible_codes:
        print(f"\n可访问的代码列表: {accessible_codes}")

    return results

if __name__ == "__main__":
    results = main()