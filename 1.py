import requests

cookies = {
    'JSESSIONID': '5457B798BEACD8FA896071DB7528046C',
}

headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/x-www-form-urlencoded',
    'DNT': '1',
    'Origin': 'http://search.gjsy.gov.cn',
    'Pragma': 'no-cache',
    'Proxy-Connection': 'keep-alive',
    'Referer': 'http://search.gjsy.gov.cn/index.jsp?t=12&c=130100&at=0&sn=1&mc=20',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
}

data = {
  'c': '130100',
  't': '12',
  'at': '0',
  'sn': '1',
  'mc': '20',
  'ts': '1753929667248',
  'w': 'null',
  'h': 'null'
}

response = requests.post('http://search.gjsy.gov.cn/bjtz/query', headers=headers, cookies=cookies, data=data, verify=False)
