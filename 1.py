import requests
import time
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

def test_url_accessibility(code):
    """测试指定代码的URL是否可访问"""

    cookies = {
        'JSESSIONID': '5457B798BEACD8FA896071DB7528046C',
    }

    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'DNT': '1',
        'Origin': 'http://search.gjsy.gov.cn',
        'Pragma': 'no-cache',
        'Proxy-Connection': 'keep-alive',
        'Referer': f'http://search.gjsy.gov.cn/index.jsp?t=12&c={code}&at=0&sn=1&mc=20',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
    }

    data = {
        'c': str(code),
        't': '12',
        'at': '0',
        'sn': '1',
        'mc': '20',
        'ts': str(int(time.time() * 1000)),  # 使用当前时间戳
        'w': 'null',
        'h': 'null'
    }

    try:
        response = requests.post('http://search.gjsy.gov.cn/bjtz/query',
                               headers=headers,
                               cookies=cookies,
                               data=data,
                               verify=False,
                               timeout=15)  # 设置15秒超时，多线程时可能需要更长时间

        status_code = response.status_code
        is_accessible = status_code == 200

        # 获取当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 判断响应内容是否有效（可以根据实际情况调整判断逻辑）
        content_valid = len(response.text) > 100  # 简单判断：内容长度大于100字符

        print(f"[{current_time}] 代码: {code} | 状态码: {status_code} | 可访问: {'是' if is_accessible else '否'} | 内容有效: {'是' if content_valid else '否'}")

        return {
            'code': code,
            'status_code': status_code,
            'is_accessible': is_accessible,
            'content_valid': content_valid,
            'response_length': len(response.text),
            'timestamp': current_time
        }

    except requests.exceptions.RequestException as e:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] 代码: {code} | 错误: {str(e)} | 可访问: 否")

        return {
            'code': code,
            'status_code': None,
            'is_accessible': False,
            'content_valid': False,
            'error': str(e),
            'timestamp': current_time
        }

def main(max_workers=10):
    """
    主函数：使用多线程测试从659100到659999的所有代码

    参数:
        max_workers: 最大线程数，默认为10
    """
    start_code = 659100
    end_code = 659999
    total_codes = end_code - start_code + 1

    print("开始测试网页可访问性...")
    print(f"测试范围：{start_code} - {end_code}")
    print(f"使用多线程并发测试，最大线程数：{max_workers}")
    print("-" * 80)

    # 创建一个线程安全的队列来存储结果
    results_queue = queue.Queue()

    # 创建一个锁用于打印输出
    print_lock = threading.Lock()

    # 进度计数器
    completed_count = 0

    # 定义回调函数，用于处理完成的任务
    def task_done_callback(future):
        nonlocal completed_count
        result = future.result()
        results_queue.put(result)

        with print_lock:
            nonlocal completed_count
            completed_count += 1
            print(f"进度: {completed_count}/{total_codes} ({completed_count/total_codes*100:.2f}%)")

    # 使用线程池执行器
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = []
        for code in range(start_code, end_code + 1):
            future = executor.submit(test_url_accessibility, code)
            future.add_done_callback(task_done_callback)
            futures.append(future)

        print(f"已提交 {len(futures)} 个任务到线程池")

        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                # 这里只是确保任何异常都被处理
                future.result()
            except Exception as e:
                with print_lock:
                    print(f"任务执行出错: {str(e)}")

    # 从队列中获取所有结果
    results = []
    while not results_queue.empty():
        results.append(results_queue.get())

    print("-" * 80)
    print("测试完成！")

    # 统计结果
    accessible_count = sum(1 for r in results if r['is_accessible'])
    total_count = len(results)

    print(f"总测试数量: {total_count}")
    print(f"可访问数量: {accessible_count}")
    print(f"不可访问数量: {total_count - accessible_count}")

    if total_count > 0:
        print(f"可访问率: {accessible_count/total_count*100:.2f}%")
    else:
        print("可访问率: 无法计算（没有测试数据）")

    # 显示可访问的代码列表
    accessible_codes = sorted([r['code'] for r in results if r['is_accessible']])
    if accessible_codes:
        print("\n可访问的代码列表:")
        # 每行打印10个代码
        for i in range(0, len(accessible_codes), 10):
            print(", ".join(map(str, accessible_codes[i:i+10])))

    # 保存结果到文件
    try:
        with open(f"测试结果_{start_code}_{end_code}.txt", "w", encoding="utf-8") as f:
            f.write(f"测试范围: {start_code} - {end_code}\n")
            f.write(f"总测试数量: {total_count}\n")
            f.write(f"可访问数量: {accessible_count}\n")
            f.write(f"不可访问数量: {total_count - accessible_count}\n")
            if total_count > 0:
                f.write(f"可访问率: {accessible_count/total_count*100:.2f}%\n\n")

            f.write("可访问的代码列表:\n")
            f.write(", ".join(map(str, accessible_codes)))

        print(f"\n测试结果已保存到文件: 测试结果_{start_code}_{end_code}.txt")
    except Exception as e:
        print(f"保存结果到文件时出错: {str(e)}")

    return results

if __name__ == "__main__":
    # 可以在这里调整线程数
    # 建议根据网络情况和服务器负载能力调整
    # 线程数太多可能会被服务器限制或封IP
    max_workers = 20  # 可以调整这个数值，建议10-50之间

    print(f"准备使用 {max_workers} 个线程进行并发测试")
    print("注意：如果遇到连接问题，可以减少线程数")
    print()

    start_time = time.time()
    results = main(max_workers)
    end_time = time.time()

    print(f"\n总耗时: {end_time - start_time:.2f} 秒")
    print(f"平均每个测试耗时: {(end_time - start_time)/len(results):.3f} 秒")